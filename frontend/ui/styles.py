#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI样式模块
包含所有CSS样式和JavaScript代码
"""

# 自定义CSS样式 - 全屏自适应设计
CUSTOM_CSS = """
/* 全屏自适应 */
html, body {
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: auto !important;
}

.gradio-container {
    min-height: 100vh !important;
    font-size: 12px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    padding: 8px !important;
    margin: 0 !important;
    width: 100vw !important;
    max-width: 100vw !important;
    box-sizing: border-box !important;
}

/* 主容器布局 - 真正的全屏 */
.main-container {
    min-height: calc(100vh - 16px) !important;
    display: flex !important;
    gap: 12px !important;
    width: 100% !important;
    max-width: none !important;
    box-sizing: border-box !important;
}

/* 左侧配置区域 - 紧凑布局 */
.config-sidebar {
    width: 280px !important;
    min-width: 280px !important;
    max-width: 280px !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    padding: 8px 8px 8px 8px !important;
    border: 1px solid #e9ecef !important;
    overflow-y: auto !important;
    height: fit-content !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
}

/* 配置区域标题 - 更紧凑 */
.config-section {
    font-size: 14px !important;
    font-weight: bold !important;
    margin: 2px 0 2px 0 !important;
    color: #495057 !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding-bottom: 1px !important;
}
.config-section:first-child {
    margin-top: 0 !important;
}

/* 控件间距更紧凑 */
.config-sidebar .gr-box, .config-sidebar .gr-form, .config-sidebar .gr-textbox, .config-sidebar .gr-dropdown, .config-sidebar .gr-slider, .config-sidebar .gr-button {
    margin-bottom: 2px !important;
    margin-top: 0 !important;
}
.config-sidebar .gr-button { margin-bottom: 1px !important; }
.config-sidebar .gr-textbox, .config-sidebar .gr-dropdown, .config-sidebar .gr-slider {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}
.config-sidebar .gr-markdown {
    margin-bottom: 1px !important;
    margin-top: 1px !important;
    padding: 0 !important;
}

/* 右侧主要内容区域 - 充分利用剩余空间 */
.main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 16px) !important;
    width: calc(100vw - 304px) !important;
    max-width: calc(100vw - 304px) !important;
    overflow: visible !important;
}

/* 标题样式 */
.main-title {
    font-size: 28px !important;
    font-weight: bold !important;
    margin: 0 0 16px 0 !important;
    color: #2c3e50 !important;
    text-align: left !important;
}

/* 按钮样式 */
.gr-button {
    font-size: 12px !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
}

/* 小按钮 */
.small-button {
    font-size: 11px !important;
    padding: 4px 8px !important;
    min-height: 32px !important;
}

/* 输入框样式 */
.gr-textbox {
    font-size: 12px !important;
}

/* Markdown渲染 */
.gr-markdown {
    font-size: 13px !important;
    line-height: 1.5 !important;
}

/* 标签页样式 */
.gr-tab-nav {
    font-size: 13px !important;
    margin-bottom: 12px !important;
}

/* 表单间距 */
.gr-form {
    gap: 6px !important;
}

/* 面板样式 */
.gr-panel {
    padding: 8px !important;
    border-radius: 6px !important;
}

/* 组件边界 - 紧凑布局 */
.component-border {
    border: 1px solid #dee2e6 !important;
    border-radius: 6px !important;
    padding: 8px !important;
    margin-bottom: 2px !important;
    overflow-y: auto !important;
    resize: vertical !important; 
    min-height: 200px !important;
    height: auto !important;
    position: relative !important;
}

/* 固定高度的组件 - 思考过程、知识库参考、对话历史、数据参考 */
.component-border.fixed-height.reasoning-content {
    max-height: 250px !important;
    height: 250px !important;
}
.component-border.fixed-height.reference-content {
    max-height: 800px !important;
    height: 650px !important;
}
.component-border.fixed-height.history-content {
    max-height: 300px !important;
    height: 300px !important;
}
.component-border.fixed-height.data-reference-content {
    max-height: 800px !important;
    height: 650px !important;
}

/* 动态高度的组件 - 回复内容 */
.component-border.dynamic-height {
    max-height: none !important;
    height: auto !important;
    min-height: 300px !important;
}

/* 标签和耗时显示 - 紧贴内容框 */
.label-with-timing {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 2px !important;
    margin-top: 8px !important;
    padding: 4px 8px !important;
    background: #f8f9fa !important;
    border-radius: 4px !important;
    border: 1px solid #e9ecef !important;
}

.timing-display {
    font-size: 11px !important;
    color: #6c757d !important;
    font-weight: normal !important;
    background: #fff !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    border: 1px solid #dee2e6 !important;
}

/* 知识库参考和数据参考内容字体 */
.reference-content {
    font-size: 11px !important;
    line-height: 1.4 !important;
}

/* 思考过程内容字体 */
.reasoning-content {
    font-size: 11px !important;
    line-height: 1.4 !important;
}

/* Gradio内置复制按钮样式修复 - 确保不被圆角边框遮挡 */
.gr-markdown .copy-button,
.gr-textbox .copy-button,
[data-testid="markdown"] .copy-button {
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    background: #007bff !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-size: 10px !important;
    cursor: pointer !important;
    z-index: 1002 !important;
    opacity: 0.9 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    margin: 0 !important;
}

.gr-markdown .copy-button:hover,
.gr-textbox .copy-button:hover,
[data-testid="markdown"] .copy-button:hover {
    opacity: 1 !important;
    background: #0056b3 !important;
    transform: scale(1.05) !important;
}

/* 确保Markdown容器有足够的内边距给复制按钮 */
.gr-markdown,
[data-testid="markdown"] {
    position: relative !important;
    padding-right: 70px !important;
    padding-top: 35px !important;
}

/* 修复圆角边框对复制按钮的遮挡 */
.component-border .gr-markdown,
.component-border [data-testid="markdown"] {
    border-radius: 6px !important;
    overflow: visible !important;
}

/* Markdown组件样式 */
.gr-markdown {
    max-height: none !important;
    overflow-y: auto !important;
}

/* 标签页内容区域 */
.tab-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: visible !important;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .config-sidebar {
        width: 200px !important;
        min-width: 200px !important;
        max-width: 200px !important;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column !important;
    }
    .config-sidebar {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
    }
}

/* 配置区域标题 */
.config-section {
    font-size: 14px !important;
    font-weight: bold !important;
    margin: 12px 0 8px 0 !important;
    color: #495057 !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding-bottom: 4px !important;
}

.config-section:first-child {
    margin-top: 0 !important;
}

/* 隐藏未使用的标签变量 */
.hidden-label {
    display: none !important;
}
"""
