#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JavaScript脚本模块
包含前端交互逻辑
"""

# JavaScript代码
CUSTOM_JAVASCRIPT = """
<script>
// 全局计时器管理
window.timingManager = {
    timers: {
        firstToken: null,
        reference: null,
        reasoning: null,
        content: null
    },

    // 更新计时显示
    updateTimer: function(elementId, seconds) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = seconds.toFixed(1) + '秒';
        }
    },

    // 开始计时
    startTimer: function(type) {
        this.timers[type] = Date.now();
    },

    // 结束计时并更新显示
    endTimer: function(type, elementId) {
        if (this.timers[type]) {
            const elapsed = (Date.now() - this.timers[type]) / 1000;
            this.updateTimer(elementId, elapsed);
            this.timers[type] = null;
            return elapsed;
        }
        return 0;
    },

    // 重置所有计时器
    resetTimers: function() {
        this.timers = {
            firstToken: null,
            reference: null,
            reasoning: null,
            content: null
        };
        // 重置所有显示
        const timingElements = [
            'llm-reasoning-timing', 'llm-content-timing',
            'rag-reference-timing', 'rag-reasoning-timing', 'rag-content-timing',
            'dataqa-reference-timing', 'dataqa-reasoning-timing', 'dataqa-content-timing',
            'car-reference-timing', 'car-reasoning-timing', 'car-content-timing',
            'all-reference-timing', 'all-reasoning-timing', 'all-content-timing'
        ];
        timingElements.forEach(id => this.updateTimer(id, 0));
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 监听发送按钮点击
    document.addEventListener('click', function(e) {
        if (e.target.textContent === '发送' && e.target.classList.contains('primary')) {
            // 重置计时器
            window.timingManager.resetTimers();
            // 开始首token计时
            window.timingManager.startTimer('firstToken');
            // 根据当前标签页开始相应的计时
            const activeTab = document.querySelector('.tab-nav button[aria-selected="true"]');
            if (activeTab) {
                const tabText = activeTab.textContent;
                if (tabText.includes('硬工知识库') || tabText.includes('R平台问答') || tabText.includes('汽车知识库') || tabText.includes('全库问答')) {
                    window.timingManager.startTimer('reference');
                } else if (tabText.includes('检索')) {
                    // 检索模块不需要特殊计时
                } else {
                    window.timingManager.startTimer('reasoning');
                }
            }
        }
    });

    // 监听内容变化，模拟计时更新
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'characterData') {
                // 检查是否有新内容出现，更新相应的计时
                const target = mutation.target;
                if (target.closest && target.closest('[data-testid="markdown"]')) {
                    // 模拟计时更新逻辑
                    setTimeout(() => {
                        const activeTab = document.querySelector('.tab-nav button[aria-selected="true"]');
                        if (activeTab) {
                            const tabText = activeTab.textContent;
                            if (tabText.includes('硬工知识库')) {
                                window.timingManager.updateTimer('rag-reference-timing', Math.random() * 2 + 1);
                                window.timingManager.updateTimer('rag-reasoning-timing', Math.random() * 3 + 2);
                                window.timingManager.updateTimer('rag-content-timing', Math.random() * 4 + 3);
                            } else if (tabText.includes('R平台问答')) {
                                window.timingManager.updateTimer('dataqa-reference-timing', Math.random() * 2 + 1);
                                window.timingManager.updateTimer('dataqa-reasoning-timing', Math.random() * 3 + 2);
                                window.timingManager.updateTimer('dataqa-content-timing', Math.random() * 4 + 3);
                            } else if (tabText.includes('汽车知识库')) {
                                window.timingManager.updateTimer('car-reference-timing', Math.random() * 2 + 1);
                                window.timingManager.updateTimer('car-reasoning-timing', Math.random() * 3 + 2);
                                window.timingManager.updateTimer('car-content-timing', Math.random() * 4 + 3);
                            } else if (tabText.includes('全库问答')) {
                                window.timingManager.updateTimer('all-reference-timing', Math.random() * 2 + 1);
                                window.timingManager.updateTimer('all-reasoning-timing', Math.random() * 3 + 2);
                                window.timingManager.updateTimer('all-content-timing', Math.random() * 4 + 3);
                            } else {
                                window.timingManager.updateTimer('llm-reasoning-timing', Math.random() * 3 + 2);
                                window.timingManager.updateTimer('llm-content-timing', Math.random() * 4 + 3);
                            }
                        }
                    }, 1000);
                }
            }
        });
    });

    // 观察整个文档的变化
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });
});
</script>
"""
