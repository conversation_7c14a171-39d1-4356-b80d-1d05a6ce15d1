#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI组件模块
包含Gradio界面组件的创建和配置
"""

import gradio as gr
import asyncio
import os
import sys
from typing import Callable

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入配置
try:
    from frontend.frontend_config import DEFAULT_MODELS, DEFAULT_TOP_K, DEFAULT_MODEL, TITLE
    from loguru import logger
    logger.info("成功导入前端配置文件")
except ImportError:
    from loguru import logger
    logger.info("前端配置文件不存在，使用默认值")
    DEFAULT_MODELS = ["qwen3_32b", "gpt-4", "claude-3"]
    DEFAULT_TOP_K = 3
    DEFAULT_MODEL = "qwen3_32b"
    TITLE = "🤖 问题库AI"

from frontend.ui.styles import CUSTOM_CSS
from frontend.ui.scripts import CUSTOM_JAVASCRIPT


class UIComponents:
    """UI组件管理器"""
    
    def __init__(self):
        """初始化UI组件管理器"""
        self.components = {}
    
    def create_config_sidebar(self) -> dict:
        """创建配置侧边栏

        Returns:
            包含所有配置组件的字典
        """
        with gr.Column(elem_classes=["config-sidebar"]):
            # 主标题
            gr.HTML('<h1 class="sidebar-title">🤖 问题库AI</h1>')

            # 对话配置
            gr.Markdown("### 对话配置", elem_classes=["config-section"])
            user_id = gr.Textbox(
                label="用户ID",
                value="user123",
                placeholder="请输入用户ID",
                elem_classes=["small-input"]
            )
            conversation_id = gr.Textbox(
                label="对话ID",
                value="conv123",
                placeholder="请输入对话ID",
                elem_classes=["small-input"]
            )
            first_token_time = gr.Textbox(
                label="首token响应时间",
                value="0.0秒",
                interactive=False,
                elem_classes=["small-input"]
            )
            
            # 模型配置
            gr.Markdown("### 模型配置", elem_classes=["config-section"])
            model_id = gr.Dropdown(
                label="模型选择",
                choices=DEFAULT_MODELS,
                value=DEFAULT_MODEL,
                elem_classes=["small-dropdown"]
            )
            
            # 检索配置
            gr.Markdown("### 检索配置", elem_classes=["config-section"])
            top_k = gr.Slider(
                label="检索数量",
                minimum=1,
                maximum=50,
                value=DEFAULT_TOP_K,
                step=1,
                elem_classes=["small-slider"]
            )
            
            # 历史清理
            gr.Markdown("### 历史管理", elem_classes=["config-section"])
            clear_all_btn = gr.Button(
                "清空所有历史",
                variant="secondary",
                elem_classes=["small-button"]
            )
            clear_llm_btn = gr.Button(
                "清空LLM历史",
                variant="secondary",
                elem_classes=["small-button"]
            )
            clear_rag_btn = gr.Button(
                "清空RAG历史",
                variant="secondary",
                elem_classes=["small-button"]
            )
            clear_dataqa_btn = gr.Button(
                "清空DATAQA历史",
                variant="secondary",
                elem_classes=["small-button"]
            )
            clear_car_btn = gr.Button(
                "清空汽车知识库历史",
                variant="secondary",
                elem_classes=["small-button"]
            )
            clear_all_qa_btn = gr.Button(
                "清空全库问答历史",
                variant="secondary",
                elem_classes=["small-button"]
            )
        
        return {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "first_token_time": first_token_time,
            "model_id": model_id,
            "top_k": top_k,
            "clear_all_btn": clear_all_btn,
            "clear_llm_btn": clear_llm_btn,
            "clear_rag_btn": clear_rag_btn,
            "clear_dataqa_btn": clear_dataqa_btn,
            "clear_car_btn": clear_car_btn,
            "clear_all_qa_btn": clear_all_qa_btn
        }
    
    def create_chat_interface(self, tab_name: str, has_reference: bool = False) -> dict:
        """创建聊天界面

        Args:
            tab_name: 标签页名称
            has_reference: 是否包含参考内容

        Returns:
            包含聊天界面组件的字典
        """
        components = {}

        with gr.Column(elem_classes=["tab-content"]):
            # 用户输入区域
            with gr.Row():
                query_input = gr.Textbox(
                    label="请输入您的问题",
                    placeholder="在这里输入您的问题...",
                    lines=2,
                    scale=4
                )
                send_btn = gr.Button(
                    "发送",
                    variant="primary",
                    scale=1,
                    elem_classes=["small-button"]
                )

            components["query_input"] = query_input
            components["send_btn"] = send_btn

            # 响应显示区域 - 新布局
            if has_reference:
                # 主要内容区域：左侧知识库参考，右侧思考过程和回复
                with gr.Row(elem_classes=["main-response-area"]):
                    # 左侧：知识库参考
                    with gr.Column(scale=1, elem_classes=["left-reference-column"]):
                        gr.HTML(f'<div class="label-with-timing"><strong>知识库参考</strong><span id="{tab_name.lower()}-reference-timing" class="timing-display">0.0秒</span></div>')
                        reference_output = gr.Markdown(
                            value="",
                            elem_classes=["component-border", "fixed-height", "reference-content"],
                            show_copy_button=True
                        )
                        components["reference_output"] = reference_output

                    # 右侧：思考过程和回复内容
                    with gr.Column(scale=1, elem_classes=["right-content-column"]):
                        # 思考过程 - 支持折叠
                        with gr.Accordion("思考过程", open=True, elem_classes=["collapsible-reasoning"]):
                            gr.HTML(f'<div class="label-with-timing"><span id="{tab_name.lower()}-reasoning-timing" class="timing-display">0.0秒</span></div>')
                            reasoning_output = gr.Markdown(
                                value="",
                                elem_classes=["component-border", "fixed-height", "reasoning-content"],
                                show_copy_button=True
                            )
                            components["reasoning_output"] = reasoning_output

                        # 回复内容
                        gr.HTML(f'<div class="label-with-timing"><strong>回复内容</strong><span id="{tab_name.lower()}-content-timing" class="timing-display">0.0秒</span></div>')
                        content_output = gr.Markdown(
                            value="",
                            elem_classes=["component-border", "dynamic-height"],
                            show_copy_button=True
                        )
                        components["content_output"] = content_output
            else:
                # 无参考内容的布局（LLM问答）
                # 思考过程 - 支持折叠
                with gr.Accordion("思考过程", open=True, elem_classes=["collapsible-reasoning"]):
                    gr.HTML(f'<div class="label-with-timing"><span id="{tab_name.lower()}-reasoning-timing" class="timing-display">0.0秒</span></div>')
                    reasoning_output = gr.Markdown(
                        value="",
                        elem_classes=["component-border", "fixed-height", "reasoning-content"],
                        show_copy_button=True
                    )
                    components["reasoning_output"] = reasoning_output

                # 回复内容
                gr.HTML(f'<div class="label-with-timing"><strong>回复内容</strong><span id="{tab_name.lower()}-content-timing" class="timing-display">0.0秒</span></div>')
                content_output = gr.Markdown(
                    value="",
                    elem_classes=["component-border", "dynamic-height"],
                    show_copy_button=True
                )
                components["content_output"] = content_output

            # 对话历史
            with gr.Row():
                with gr.Column():
                    gr.HTML('<div class="label-with-timing"><strong>对话历史</strong></div>')
                    history_output = gr.Markdown(
                        value="",
                        elem_classes=["component-border", "fixed-height", "history-content"],
                        show_copy_button=True
                    )
                    components["history_output"] = history_output

        return components
    
    def create_search_interface(self) -> dict:
        """创建搜索界面

        Returns:
            包含搜索界面组件的字典
        """
        components = {}

        with gr.Column(elem_classes=["tab-content"]):
            # 用户输入区域
            with gr.Row():
                query_input = gr.Textbox(
                    label="请输入检索关键词",
                    placeholder="在这里输入检索关键词...",
                    lines=2,
                    scale=4
                )
                search_btn = gr.Button(
                    "检索",
                    variant="primary",
                    scale=1,
                    elem_classes=["small-button"]
                )

            components["query_input"] = query_input
            components["search_btn"] = search_btn

            # 检索结果（数据参考）
            with gr.Row():
                with gr.Column():
                    gr.HTML('<div class="label-with-timing"><strong>数据参考</strong></div>')
                    search_output = gr.Markdown(
                        value="",
                        elem_classes=["component-border", "fixed-height", "data-reference-content"],
                        show_copy_button=True
                    )
                    components["search_output"] = search_output

        return components
    
    def wrap_async_generator(self, async_gen_func: Callable) -> Callable:
        """包装异步生成器为同步生成器
        
        Args:
            async_gen_func: 异步生成器函数
            
        Returns:
            同步生成器函数
        """
        def sync_wrapper(*args, **kwargs):
            async def async_gen():
                async for result in async_gen_func(*args, **kwargs):
                    yield result
            
            # 运行异步生成器
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                gen = async_gen()
                while True:
                    try:
                        yield loop.run_until_complete(gen.__anext__())
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return sync_wrapper
    
    def get_full_css_and_js(self) -> str:
        """获取完整的CSS和JavaScript代码
        
        Returns:
            包含CSS和JavaScript的HTML字符串
        """
        return f"""
        <style>
        {CUSTOM_CSS}
        </style>
        {CUSTOM_JAVASCRIPT}
        """
