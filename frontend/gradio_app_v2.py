#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Gradio的前端应用 - 重构版本
实现LLM问答、RAG问答、DATAQA问答的Web界面
"""

import gradio as gr
import os
import sys
from loguru import logger

# 设置日志
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'log')
os.makedirs(log_dir, exist_ok=True)
logger.remove()
log_path = os.path.join(log_dir, 'frontend.log')
logger.add(log_path, rotation="20 MB", retention="10 days", encoding="utf-8", enqueue=True, backtrace=True, diagnose=True, level="DEBUG")
logger.add(sys.stdout, level="INFO")
logger.info(f"日志初始化完成，日志路径: {log_path}")

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入配置
try:
    from frontend.frontend_config import (
        API_BASE_URL, DEFAULT_MODELS, DEFAULT_TOP_K, DEFAULT_MODEL,
        THEME, TITLE, REQUEST_TIMEOUT,
        GRADIO_HOST, GRADIO_PORT, GRADIO_SHARE, GRADIO_DEBUG
    )
    logger.info("成功导入前端配置文件")
except ImportError:
    logger.info("前端配置文件不存在，使用默认值")
    API_BASE_URL = "http://dms.ai.xiaomi.com/api/v1"
    DEFAULT_MODELS = ["qwen3_32b", "gpt-4", "claude-3"]
    DEFAULT_TOP_K = 3
    DEFAULT_MODEL = "qwen3_32b"
    THEME = "soft"
    TITLE = "🤖 问题库AI"
    REQUEST_TIMEOUT = 600.0
    GRADIO_HOST = "0.0.0.0"
    GRADIO_PORT = 7860
    GRADIO_SHARE = True
    GRADIO_DEBUG = True

# 导入模块化组件
from frontend.core.chat_app import ChatApp
from frontend.api.client import APIClient
from frontend.handlers.chat_handlers import (
    LLMChatHandler, RAGChatHandler, DataQAChatHandler,
    CarChatHandler, AllChatHandler, SearchHandler
)
from frontend.ui.components import UIComponents


def create_gradio_interface():
    """创建Gradio界面"""
    logger.info("开始创建Gradio界面")

    # 初始化组件
    app = ChatApp(API_BASE_URL)
    api_client = APIClient(API_BASE_URL)
    ui_components = UIComponents()

    # 初始化处理器
    llm_handler = LLMChatHandler(api_client, app)
    rag_handler = RAGChatHandler(api_client, app)
    dataqa_handler = DataQAChatHandler(api_client, app)
    car_handler = CarChatHandler(api_client, app)
    all_handler = AllChatHandler(api_client, app)
    search_handler = SearchHandler(api_client, app)

    # 包装异步生成器为同步生成器
    sync_llm_chat = ui_components.wrap_async_generator(llm_handler.chat_stream)
    sync_rag_chat = ui_components.wrap_async_generator(rag_handler.chat_stream)
    sync_dataqa_chat = ui_components.wrap_async_generator(dataqa_handler.chat_stream)
    sync_car_chat = ui_components.wrap_async_generator(car_handler.chat_stream)
    sync_all_chat = ui_components.wrap_async_generator(all_handler.chat_stream)
    sync_search = ui_components.wrap_async_generator(search_handler.search_stream)

    # 创建Gradio界面
    with gr.Blocks(
        theme=THEME,
        css=ui_components.get_full_css_and_js(),
        title=TITLE
    ) as interface:

        with gr.Row(elem_classes=["main-container"]):
            # 左侧配置区域
            config_components = ui_components.create_config_sidebar()

            # 右侧主要内容区域
            with gr.Column(elem_classes=["main-content"]):
                # 主标题
                gr.HTML(f'<h1 class="main-title">{TITLE}</h1>')

                # 标签页
                with gr.Tabs():
                    # LLM问答标签页
                    with gr.Tab("💬 LLM问答"):
                        llm_components = ui_components.create_chat_interface("llm", has_reference=False)

                    # RAG问答标签页
                    with gr.Tab("📚 硬工知识库"):
                        rag_components = ui_components.create_chat_interface("rag", has_reference=True)

                    # DATAQA问答标签页
                    with gr.Tab("🔍 R平台问答"):
                        dataqa_components = ui_components.create_chat_interface("dataqa", has_reference=True)

                    # 汽车知识库标签页
                    with gr.Tab("🚗 汽车知识库"):
                        car_components = ui_components.create_chat_interface("car", has_reference=True)

                    # 全库问答标签页
                    with gr.Tab("🌐 全库问答"):
                        all_components = ui_components.create_chat_interface("all", has_reference=True)

                    # 检索标签页
                    with gr.Tab("🔎 检索"):
                        search_components = ui_components.create_search_interface()

        # 绑定事件处理器
        _bind_event_handlers(
            config_components, llm_components, rag_components, dataqa_components,
            car_components, all_components, search_components,
            app, sync_llm_chat, sync_rag_chat, sync_dataqa_chat,
            sync_car_chat, sync_all_chat, sync_search
        )

    logger.info("Gradio界面创建完成")
    return interface


def _bind_event_handlers(config_components, llm_components, rag_components, dataqa_components,
                        car_components, all_components, search_components,
                        app, sync_llm_chat, sync_rag_chat, sync_dataqa_chat,
                        sync_car_chat, sync_all_chat, sync_search):
    """绑定事件处理器"""

    # 清空历史按钮事件
    config_components["clear_all_btn"].click(
        lambda: app.clear_history("all"),
        outputs=[
            llm_components["history_output"], rag_components["history_output"],
            dataqa_components["history_output"], car_components["history_output"],
            all_components["history_output"]
        ]
    )

    config_components["clear_llm_btn"].click(
        lambda: app.clear_history("llm"),
        outputs=[llm_components["history_output"]]
    )

    config_components["clear_rag_btn"].click(
        lambda: app.clear_history("rag"),
        outputs=[rag_components["history_output"]]
    )

    config_components["clear_dataqa_btn"].click(
        lambda: app.clear_history("dataqa"),
        outputs=[dataqa_components["history_output"]]
    )

    config_components["clear_car_btn"].click(
        lambda: app.clear_history("car"),
        outputs=[car_components["history_output"]]
    )

    config_components["clear_all_qa_btn"].click(
        lambda: app.clear_history("allqa"),
        outputs=[all_components["history_output"]]
    )

    # LLM问答事件
    llm_components["send_btn"].click(
        sync_llm_chat,
        inputs=[
            llm_components["query_input"],
            config_components["model_id"],
            config_components["user_id"],
            llm_components["history_output"]
        ],
        outputs=[
            llm_components["history_output"],
            llm_components["reasoning_output"],
            llm_components["content_output"]
        ]
    )

    # RAG问答事件
    rag_components["send_btn"].click(
        sync_rag_chat,
        inputs=[
            rag_components["query_input"],
            config_components["model_id"],
            config_components["user_id"],
            config_components["top_k"],
            rag_components["history_output"]
        ],
        outputs=[
            rag_components["history_output"],
            rag_components["reference_output"],
            rag_components["reasoning_output"],
            rag_components["content_output"]
        ]
    )

    # DATAQA问答事件
    dataqa_components["send_btn"].click(
        sync_dataqa_chat,
        inputs=[
            dataqa_components["query_input"],
            config_components["model_id"],
            config_components["user_id"],
            config_components["top_k"],
            dataqa_components["history_output"]
        ],
        outputs=[
            dataqa_components["history_output"],
            dataqa_components["reference_output"],
            dataqa_components["reasoning_output"],
            dataqa_components["content_output"]
        ]
    )

    # 汽车知识库问答事件
    car_components["send_btn"].click(
        sync_car_chat,
        inputs=[
            car_components["query_input"],
            config_components["model_id"],
            config_components["user_id"],
            config_components["top_k"],
            car_components["history_output"]
        ],
        outputs=[
            car_components["history_output"],
            car_components["reference_output"],
            car_components["reasoning_output"],
            car_components["content_output"]
        ]
    )

    # 全库问答事件
    all_components["send_btn"].click(
        sync_all_chat,
        inputs=[
            all_components["query_input"],
            config_components["model_id"],
            config_components["user_id"],
            config_components["top_k"],
            all_components["history_output"]
        ],
        outputs=[
            all_components["history_output"],
            all_components["reference_output"],
            all_components["reasoning_output"],
            all_components["content_output"]
        ]
    )

    # 检索事件
    search_components["search_btn"].click(
        sync_search,
        inputs=[
            search_components["query_input"],
            config_components["user_id"],
            config_components["top_k"]
        ],
        outputs=[search_components["search_output"]]
    )